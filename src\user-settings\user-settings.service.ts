import { Injectable } from '@nestjs/common';
import { UserSettingsRepository } from './user-settings.repository';
import { tbl_user_setting } from '@prisma/client';
import { UtilsService } from 'src/common/utils/utils.service';
import { SettingsRepository } from 'src/settings/settings.repository';

@Injectable()
export class UserSettingsService {
  constructor(
    private readonly userSettingsRepository: UserSettingsRepository,
    private readonly orgSettingsRepository: SettingsRepository,
    private readonly utilsService: UtilsService,
  ) {}

  async getUserSetting(
    userEmail: string,
    settingType: string,
  ): Promise<tbl_user_setting> {
    const existingUserSetting =
      await this.userSettingsRepository.getUserSetting(userEmail, settingType);

    if (existingUserSetting) {
      return existingUserSetting;
    }
    let orgActualSetting =
      await this.orgSettingsRepository.getSettingByConfigType(null);

    let filteredSetting = {
      userEmail: userEmail,
      settingType: settingType,
      settingValue: { setting: orgActualSetting },
    };
  }
}
