import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { tbl_org_setting } from '@prisma/client';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';
import { CreateSettingDto, UpdateSettingDto } from './dto/settings.dto';

@Injectable()
export class SettingsRepository {
  private readonly logger = new Logger(SettingsRepository.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async getSettingByConfigType(
    config_type: string,
  ): Promise<tbl_org_setting[]> {
    try {
      return await this.prisma.tbl_org_setting.findMany({
        where: { config_type: config_type ?? undefined, isActive: true },
      });
    } catch (error) {
      this.logger.error('Error fetching setting:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch setting',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateSetting(
    data: Partial<UpdateSettingDto>,
    userEmail: string,
  ): Promise<tbl_org_setting> {
    try {
      return await this.prisma.tbl_org_setting.update({
        where: { key: data.key },
        data: {
          key: data.key,
          value: data.value,
          config_type: data.config_type,
          data_type: data.data_type,
          description: data.description,
          updated_by: userEmail,
        },
      });
    } catch (error) {
      this.logger.error('Error updating setting:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update setting',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getSettingByKey(key: string): Promise<tbl_org_setting> {
    try {
      return await this.prisma.tbl_org_setting.findUnique({
        where: { key: key },
      });
    } catch (error) {
      this.logger.error('Error fetching setting:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch setting',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createSetting(data: CreateSettingDto, userEmail: string) {
    try {
      return await this.prisma.tbl_org_setting.create({
        data: {
          key: data.key,
          value: data.value,
          config_type: data.config_type,
          data_type: data.data_type,
          description: data.description,
          created_by: userEmail,
        },
      });
    } catch (error) {
      this.logger.error('Error creating setting:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create setting',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
