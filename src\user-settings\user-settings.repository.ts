import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class UserSettingsRepository {
  logger: Logger = new Logger(UserSettingsRepository.name);
  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async getUserSetting(userEmail: string, settingType: string) {
    try {
      return await this.prisma.tbl_user_setting.findFirst({
        where: {
          user_email: userEmail,
          setting_type: settingType ?? undefined,
        },
      });
    } catch (error) {
      this.logger.error('Error fetching user setting:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch user setting',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async createUserSetting(
    userEmail: string,
    settingType: string,
    settingValue: string,
  ) {
    try {
      return await this.prisma.tbl_user_setting.create({
        data: {
          user_email: userEmail,
          setting_type: settingType,
          setting_value: settingValue,
        },
      });
    } catch (error) {
      this.logger.error('Error creating user setting:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create user setting',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateUserSetting(
    userEmail: string,
    userSettingId: string,
    settingType: string,
    settingValue: string,
  ) {
    try {
      return await this.prisma.tbl_user_setting.update({
        where: {
          user_email: userEmail,
          user_setting_id: userSettingId,
        },
        data: {
          setting_value: settingValue,
          setting_type: settingType,
        },
      });
    } catch (error) {
      this.logger.error('Error updating user setting:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update user setting',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteUserSetting(userEmail: string, userSettingId: string) {
    try {
      return await this.prisma.tbl_user_setting.delete({
        where: {
          user_email: userEmail,
          user_setting_id: userSettingId,
        },
      });
    } catch (error) {
      this.logger.error('Error deleting user setting:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete user setting',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
