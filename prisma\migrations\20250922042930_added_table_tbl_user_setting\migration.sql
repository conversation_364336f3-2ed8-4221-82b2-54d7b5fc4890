/*
  Warnings:

  - A unique constraint covering the columns `[role_id,permission_id]` on the table `tbl_role_permission_mapping` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateTable
CREATE TABLE "tbl_user_setting" (
    "user_setting_id" UUID NOT NULL,
    "user_id" UUID,
    "user_email" TEXT NOT NULL,
    "setting_type" TEXT,
    "setting_value" JSONB NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMPTZ(3),
    "updated_by" TEXT,

    CONSTRAINT "tbl_user_setting_pkey" PRIMARY KEY ("user_setting_id")
);

-- CreateIndex
-- CREATE UNIQUE INDEX "tbl_role_permission_mapping_role_id_permission_id_key" ON "tbl_role_permission_mapping"("role_id", "permission_id");

-- AddForeignKey
ALTER TABLE "tbl_user_setting" ADD CONSTRAINT "tbl_user_setting_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "tbl_user_details"("user_id") ON DELETE SET NULL ON UPDATE CASCADE;
