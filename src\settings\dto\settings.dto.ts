import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class UpdateSettingDto {
  @ApiProperty({
    description: 'Unique key for the setting',
    example: 'note.default_type',
  })
  @IsNotEmpty()
  @IsString()
  key: string;
  @ApiProperty({
    description: 'Value of the setting',
    example: 'SOAP Note',
  })
  @IsNotEmpty()
  value: string;
  @ApiProperty({
    description: 'Config type of the setting',
    example: 'USER_SETTING',
  })
  @IsOptional()
  config_type: string;
  @ApiProperty({
    description: 'Data type of the setting',
    example: 'string',
  })
  @IsOptional()
  data_type: string;
  @ApiProperty({
    description: 'Description of the setting',
    example: 'Default note type',
  })
  @IsOptional()
  description: string;
}

export class CreateSettingDto {
  @ApiProperty({
    description: 'Unique key for the setting',
    example: 'note.default_type',
  })
  @IsNotEmpty()
  @IsString()
  key: string;
  @ApiProperty({
    description: 'Value of the setting',
    example: 'SOAP Note',
  })
  @IsNotEmpty()
  @IsString()
  value: string;
  @ApiProperty({
    description: 'Config type of the setting',
    example: 'USER_SETTING',
  })
  @IsNotEmpty()
  @IsString()
  config_type: string;
  @ApiProperty({
    description: 'Data type of the setting',
    example: 'string',
  })
  @IsOptional()
  data_type: string;
  @ApiProperty({
    description: 'Description of the setting',
    example: 'Default note type',
  })
  @IsOptional()
  description: string;
}
